{"project": "hangman", "entrypoint": "src/main.ts", "include": ["src/routes", "src/state", "src/utils", "src/views", "src/static", "src/data", "src/types.ts", "index.html"], "tasks": {"dev": "deno run --watch --allow-net --allow-read --allow-env main.ts", "deploy": "deployctl deploy --project=hangman --allow-net --allow-read --allow-env --entrypoint=main.ts"}, "imports": {"@gabriel/ ": "jsr:@gabriel/ts-pattern@^5.6.2", "@std/assert": "jsr:@std/assert@1", "@effection/effection": "jsr:@effection/effection@^3.4.0"}, "deploy": {"project": "5cd54cd6-8a70-4b90-9fc5-729863c41e65", "exclude": ["**/node_modules"], "include": ["src/**/*", "main.ts"]}}