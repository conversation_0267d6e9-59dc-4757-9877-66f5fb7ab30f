:root {
  --primary-color: #2c3e50;
  --secondary-color: #3498db;
  --success-color: #2ecc71;
  --danger-color: #e74c3c;
  --text-color: #34495e;
  --transition-speed: 300ms;
  --transition-easing: cubic-bezier(0.25, 0.1, 0.25, 1);
  --hover-scale: 1.03;

  /* Responsive sizing variables */
  --base-padding: clamp(0.5rem, 3vw, 2rem);
  --font-size-h1: clamp(1.75rem, 5vw, 2.5rem);
  --font-size-word: clamp(1.25rem, 4vw, 2.5rem);
  --font-size-btn: clamp(0.9rem, 2.5vw, 1.1rem);
  --letter-spacing: clamp(0.2rem, 1vw, 0.5rem);
  --button-size: clamp(32px, 8vw, 40px);
  --svg-width: clamp(220px, 70vw, 300px);
}

body {
  margin: 0;
  padding: var(--base-padding);
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}

header {
  text-align: center;
  margin-bottom: 5px;
  position: relative;
  z-index: 0;
  transform: translateY(0.1rem);
  pointer-events: none;
}

h1 {
  color: rgba(44, 62, 80, 0.8);
  margin: 0;
  font-size: calc(var(--font-size-h1) * 1.5);
  line-height: 1.2;
  font-weight: 800;
  letter-spacing: -0.05em;
  text-transform: uppercase;
  text-shadow:
    0 5px 10px rgba(0, 0, 0, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.2),
    2px 2px 0px #4a6fa5,
    -2px -2px 0px #c3cfe2;
  transform: perspective(500px) rotateX(10deg);
  transition: all 0.3s ease;
  position: relative;
  display: inline-block;
}

/* Create hover effect for the body container to interact with the header */
body:hover h1 {
  transform: perspective(500px) rotateX(5deg) translateY(-5px);
  text-shadow:
    0 10px 15px rgba(103, 98, 98, 0.2),
    0 4px 6px rgba(0, 0, 0, 0.2),
    3px 3px 0px #5f7ca6,
    -3px -3px 0px #c3cfe2;
  filter: brightness(1.1);
}

/* Add a subtle glow effect */
h1::after {
  content: '';
  position: absolute;
  top: 120%;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 15px;
  background: radial-gradient(ellipse at center, rgba(65, 105, 225, 0.3) 0%, rgba(65, 105, 225, 0) 60%);
  border-radius: 50%;
  z-index: -1;
  transition: all 0.3s ease;
}

body:hover h1::after {
  width: 120%;
  opacity: 0.7;
}

.description {
  color: #666;
  max-width: 600px;
  line-height: 1.6;
  margin: 0 auto 2rem;
}

.game-container {
  width: 100%;
  max-width: 800px;
  padding: var(--base-padding);
  background: #f8f9fa;
  border-radius: 1rem;
  box-shadow:
    0 10px 20px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(0, 0, 0, 0.05);
  transition: transform var(--transition-speed) var(--transition-easing);
  position: relative;
  z-index: 2;
  box-sizing: border-box;
  margin-top: -2rem;
  backdrop-filter: blur(5px);
}

.hangman-display {
  position: relative;
  width: var(--svg-width);
  margin: 0 auto;
  transition: transform var(--transition-speed) var(--transition-easing);
  background: transparent !important;
  background-color: transparent !important;
}

svg {
  width: 100%;
  height: auto;
  transform-origin: center;
  transition: all var(--transition-speed) var(--transition-easing);
  background: transparent;
}

.hangman-figure,
.celebrate-figure {
  background: transparent;
}

.hangman-part {
  stroke: var(--primary-color);
  stroke-width: 4;
  stroke-linecap: round;
  fill: transparent;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.hangman-part.visible {
  opacity: 1;
}

/* Word display */
.word-display {
  display: flex;
  justify-content: center;
  gap: clamp(0.5rem, 2vw, 1rem);
  margin: clamp(1rem, 3vw, 2rem) 0;
  font-size: var(--font-size-word);
  letter-spacing: var(--letter-spacing);
  color: var(--text-color);
  flex-wrap: wrap;
}

.letter {
  border-bottom: 4px solid var(--secondary-color);
  min-width: 1em;
  text-align: center;
  text-transform: uppercase;
}

/* Status display */
.status {
  text-align: center;
  margin: 1rem 0;
  font-size: 1.2rem;
  color: var(--text-color);
  height: 1em;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status.win {
  color: var(--success-color);
  font-weight: bold;
}

.status.lose {
  color: var(--danger-color);
  font-weight: bold;
}

/* Keyboard */
.keyboard {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(var(--button-size), 1fr));
  gap: clamp(0.25rem, 1vw, 0.5rem);
  margin-top: clamp(1rem, 3vw, 2rem);
}

.keyboard.game-over {
  opacity: 0.7;
}

button {
  padding: clamp(0.4rem, 2vw, 0.8rem);
  border: none;
  border-radius: 0.5rem;
  background: var(--secondary-color);
  color: white;
  font-size: var(--font-size-btn);
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-easing);
  min-height: var(--button-size);
  touch-action: manipulation;
}

button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  opacity: 0.7;
}

button.correct {
  background: var(--success-color);
  color: white;
  opacity: 0.8;
}

button.incorrect {
  background: var(--danger-color);
  color: white;
  opacity: 0.8;
}

button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

button:active:not(:disabled) {
  transform: translateY(1px);
}

.restart {
  background: var(--success-color);
  grid-column: span 3;
}

/* Game header and navigation */
.game-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(52, 152, 219, 0.2);
}

.game-title h2 {
  margin: 0;
  color: var(--primary-color);
  font-size: clamp(1.2rem, 3vw, 1.5rem);
}

.game-nav {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

/* Main game area */
.game-main {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

/* Game controls - for backward compatibility */
.game-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: clamp(1rem, 3vw, 1.5rem);
}

/* Difficulty selector styles - both traditional and pill-based */
.difficulty-selector {
  text-align: center;
  margin-top: 1.5rem;
  display: none;
  /* Hide the original selector */
}

.difficulty-control {
  margin: 0;
  text-align: center;
}

.difficulty-pills {
  display: flex;
  justify-content: center;
  flex-wrap: nowrap;
  gap: 0.2rem;
}

.difficulty-pills form {
  margin: 0;
  padding: 0;
  display: contents;
}

.difficulty-pill {
  flex: 0 1 auto;
  background: rgba(52, 152, 219, 0.1);
  color: var(--secondary-color);
  border: 1px solid var(--secondary-color);
  border-radius: 2rem;
  padding: 0.3rem 0.6rem;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 40px;
}

/* Keep category pills styles for backward compatibility */
.category-pills {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: clamp(0.3rem, 1vw, 0.5rem);
}

.category-pills form {
  margin: 0;
  padding: 0;
  display: contents;
}

.category-pill {
  flex: 0 1 auto;
  background: rgba(52, 152, 219, 0.1);
  color: var(--secondary-color);
  border: 1px solid var(--secondary-color);
  border-radius: 2rem;
  padding: clamp(0.3rem, 1.5vw, 0.5rem) clamp(0.8rem, 3vw, 1.2rem);
  font-size: clamp(0.85rem, 2.5vw, 0.95rem);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
}

.difficulty-pill:hover,
.category-pill:hover {
  background: rgba(52, 152, 219, 0.2);
  transform: translateY(-2px);
}

.difficulty-pill.active,
.category-pill.active {
  background: var(--secondary-color);
  color: white;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.4);
}

.difficulty-pill.easy.active {
  background: #27ae60;
  border-color: #27ae60;
  box-shadow: 0 2px 8px rgba(39, 174, 96, 0.4);
}

.difficulty-pill.medium.active {
  background: #f39c12;
  border-color: #f39c12;
  box-shadow: 0 2px 8px rgba(243, 156, 18, 0.4);
}

.difficulty-pill.hard.active {
  background: #e74c3c;
  border-color: #e74c3c;
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.4);
}

.category-pill.general.active {
  background: #8e44ad;
  border-color: #8e44ad;
  box-shadow: 0 2px 8px rgba(142, 68, 173, 0.4);
}

.category-pill.animals.active {
  background: #16a085;
  border-color: #16a085;
  box-shadow: 0 2px 8px rgba(22, 160, 133, 0.4);
}

.category-pill.countries.active {
  background: #d35400;
  border-color: #d35400;
  box-shadow: 0 2px 8px rgba(211, 84, 0, 0.4);
}

/* Category dropdown */
.category-dropdown {
  position: relative;
}

.category-dropdown select {
  padding: 0.4rem 0.8rem;
  border-radius: 2rem;
  border: 1px solid var(--secondary-color);
  background-color: white;
  color: var(--secondary-color);
  font-family: inherit;
  font-size: 0.9rem;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;utf8,<svg fill='%233498db' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  background-size: 1.2rem;
  padding-right: 2rem;
  transition: all 0.2s ease;
}

.category-dropdown select:hover {
  border-color: #2980b9;
  box-shadow: 0 2px 5px rgba(52, 152, 219, 0.2);
}

.category-dropdown select:focus {
  outline: none;
  border-color: #2980b9;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

/* Dashboard toggle button */
.dashboard-toggle {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 2.2rem;
  height: 2.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.dashboard-toggle:hover {
  background: #3a506b;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.dashboard-icon {
  font-size: 1.2rem;
}

/* Game statistics */
.game-stats {
  display: none;
  /* Hidden by default */
  flex-direction: column;
  gap: 0.5rem;
  margin: 0.5rem 0;
  padding: 0.5rem;
  border-radius: 0.5rem;
  background: rgba(52, 152, 219, 0.05);
  border: 1px solid rgba(52, 152, 219, 0.1);
  width: 100%;
  max-width: 500px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.3s ease;
}

.game-stats.visible {
  display: flex;
}

.game-stats h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
  text-align: center;
  font-size: 1.2rem;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stats-row {
  display: flex;
  justify-content: space-around;
}

.stat-box {
  text-align: center;
  padding: 0.5rem;
  min-width: 60px;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--primary-color);
}

.stat-label {
  font-size: 0.8rem;
  color: #7f8c8d;
}

/* Hint button */
.hint-container {
  display: flex;
  justify-content: center;
  /* margin: 1rem 0; */
}

.hint-button {
  background: #f1c40f;
  color: #34495e;
  border: none;
  border-radius: 2rem;
  padding: 0.5rem 1.2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.hint-button:hover:not(:disabled) {
  background: #f39c12;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.hint-button:disabled {
  background: #ecf0f1;
  color: #95a5a6;
  cursor: not-allowed;
}

.hint-icon {
  font-size: 1.2rem;
}

select {
  padding: 0.5rem;
  border-radius: 0.25rem;
  border: 1px solid #ccc;
  font-family: inherit;
  background-color: white;
}

footer {
  margin-top: 1rem;
  color: #666;
  font-size: 0.9rem;
  text-align: center;
}

@media (max-width: 600px) {
  header {
    transform: translateY(1.5rem);
  }

  h1 {
    font-size: calc(var(--font-size-h1) * 1.2);
    transform: perspective(500px) rotateX(8deg);
  }

  .game-container {
    margin-top: -1rem;
    padding: 0.8rem;
  }

  /* Adjust header for mobile */
  .game-header {
    flex-direction: column;
    gap: 0.8rem;
    align-items: center;
    padding-bottom: 0.8rem;
  }

  .game-nav {
    width: 100%;
    justify-content: space-between;
  }

  .game-title h2 {
    margin-bottom: 0.5rem;
  }

  /* Make difficulty pills more compact on mobile */
  .difficulty-pill {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    min-width: 35px;
  }

  /* Adjust category dropdown for mobile */
  .category-dropdown select {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
    padding-right: 1.8rem;
    background-size: 1rem;
  }

  /* Make dashboard toggle smaller on mobile */
  .dashboard-toggle {
    width: 2rem;
    height: 2rem;
  }

  .dashboard-icon {
    font-size: 1rem;
  }

  footer {
    margin-bottom: clamp(1rem, 3vw, 2rem);
  }

  .letter {
    border-bottom-width: 3px;
  }

  .difficulty-selector {
    margin-top: clamp(1rem, 3vw, 1.5rem);
  }

  /* Add extra touch area for better mobile experience */
  button {
    position: relative;
  }

  button::after {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    z-index: -1;
  }
}

/* Two-Player Game Styles */
.two-player-game-container {
  width: 100%;
  max-width: 1200px;
  padding: var(--base-padding);
  background: #f8f9fa;
  border-radius: 1rem;
  box-shadow:
    0 10px 20px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(0, 0, 0, 0.05);
  transition: transform var(--transition-speed) var(--transition-easing);
  position: relative;
  z-index: 2;
  box-sizing: border-box;
  margin-top: -2rem;
  backdrop-filter: blur(5px);
}

.two-player-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(52, 152, 219, 0.2);
  flex-wrap: wrap;
  gap: 1rem;
}

.two-player-header .game-title h2 {
  margin: 0;
  color: var(--primary-color);
  font-size: clamp(1.2rem, 3vw, 1.8rem);
}

.turn-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  font-size: clamp(0.9rem, 2.5vw, 1.1rem);
}

.current-turn {
  padding: 0.3rem 0.8rem;
  border-radius: 1rem;
  background: rgba(52, 152, 219, 0.1);
  color: var(--secondary-color);
  transition: all 0.3s ease;
}

.current-turn.active {
  background: var(--secondary-color);
  color: white;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.4);
  transform: scale(1.05);
}

.turn-divider {
  color: #bdc3c7;
  font-weight: normal;
}

.score-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  font-size: clamp(0.9rem, 2.5vw, 1.1rem);
  color: var(--primary-color);
}

.score-divider {
  color: #bdc3c7;
  font-weight: normal;
}

.two-player-main {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
  min-height: 400px;
}

.player-panel {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 0.8rem;
  padding: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.player-panel.active-player {
  border-color: #2ecc71;
  /* Green border for active player */
  box-shadow: 0 6px 20px rgba(46, 204, 113, 0.3);
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 1);
}

.player-panel.waiting-player {
  background: rgba(240, 240, 240, 0.8);
  /* Light gray background when waiting */
  border-color: #bdc3c7;
  opacity: 0.7;
}

.player-panel.current-player {
  border-color: var(--secondary-color);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.2);
  transform: translateY(-2px);
}

.game-divider {
  width: 3px;
  background: linear-gradient(to bottom,
      transparent 0%,
      var(--secondary-color) 20%,
      var(--secondary-color) 80%,
      transparent 100%);
  border-radius: 2px;
  margin: 1rem 0;
}

.player-game-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  height: 100%;
}

.player-header {
  text-align: center;
  width: 100%;
}

.player-header h3 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
  font-size: clamp(1rem, 2.5vw, 1.3rem);
}

.player-status {
  font-size: clamp(0.8rem, 2vw, 1rem);
  font-weight: 500;
  color: var(--text-color);
}

.player-hangman {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 200px;
}

.player-hangman .hangman-display {
  width: 100%;
  max-width: 180px;
}

.player-word {
  width: 100%;
  display: flex;
  justify-content: center;
}

.player-word .word-display {
  font-size: clamp(1rem, 3vw, 1.5rem);
  gap: clamp(0.3rem, 1.5vw, 0.8rem);
}

.player-wrong-guesses {
  font-size: clamp(0.8rem, 2vw, 1rem);
  color: var(--text-color);
  text-align: center;
}

.wrong-label {
  font-weight: 500;
}

.wrong-count {
  font-weight: 700;
  color: var(--danger-color);
}

/* Shared input area styles */
.shared-input-area {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 0.8rem;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.input-header {
  text-align: center;
  margin-bottom: 1rem;
}

.input-header h4 {
  margin: 0;
  color: var(--primary-color);
  font-size: clamp(1rem, 2.5vw, 1.2rem);
  font-weight: 600;
}

/* Two-player game over styles */
.two-player-game-over {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 0.8rem;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-top: 1rem;
}

.two-player-game-over.win {
  background: rgba(46, 204, 113, 0.1);
  border: 2px solid var(--success-color);
}

.two-player-game-over.lose {
  background: rgba(231, 76, 60, 0.1);
  border: 2px solid var(--danger-color);
}

.game-over-message {
  font-size: clamp(1.1rem, 3vw, 1.4rem);
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

.final-scores {
  font-size: clamp(0.9rem, 2.5vw, 1.1rem);
  color: var(--text-color);
  font-weight: 500;
}

/* Mobile responsiveness for two-player mode */
@media (max-width: 768px) {
  .two-player-game-container {
    margin-top: -1rem;
    padding: 0.8rem;
  }

  .two-player-header {
    flex-direction: column;
    gap: 0.8rem;
    align-items: center;
    text-align: center;
  }

  .two-player-main {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 1rem;
    min-height: auto;
  }

  .game-divider {
    width: 100%;
    height: 3px;
    background: linear-gradient(to right,
        transparent 0%,
        var(--secondary-color) 20%,
        var(--secondary-color) 80%,
        transparent 100%);
    margin: 0.5rem 0;
  }

  .player-panel {
    padding: 0.8rem;
  }

  .player-hangman {
    max-width: 150px;
  }

  .player-word .word-display {
    font-size: clamp(0.9rem, 4vw, 1.2rem);
    gap: clamp(0.2rem, 2vw, 0.5rem);
  }

  .shared-input-area {
    padding: 1rem;
  }

  .turn-indicator {
    flex-direction: column;
    gap: 0.3rem;
  }

  .turn-divider {
    display: none;
  }

  .score-display {
    flex-direction: column;
    gap: 0.3rem;
  }

  .score-divider {
    display: none;
  }
}

/* Game Mode Selector Styles */
.game-mode-selector {
  width: 100%;
  max-width: 800px;
  padding: var(--base-padding);
  background: #f8f9fa;
  border-radius: 1rem;
  box-shadow:
    0 10px 20px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(0, 0, 0, 0.05);
  transition: transform var(--transition-speed) var(--transition-easing);
  position: relative;
  z-index: 2;
  box-sizing: border-box;
  margin-top: -2rem;
  backdrop-filter: blur(5px);
}

.mode-header {
  text-align: center;
  margin-bottom: 2rem;
}

.mode-header h2 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
  font-size: clamp(1.5rem, 4vw, 2rem);
}

.mode-header p {
  margin: 0;
  color: var(--text-color);
  font-size: clamp(0.9rem, 2.5vw, 1.1rem);
}

.mode-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.mode-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.mode-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--secondary-color);
}

.mode-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.mode-card h3 {
  margin: 0 0 1rem 0;
  color: var(--primary-color);
  font-size: clamp(1.2rem, 3vw, 1.5rem);
}

.mode-card p {
  margin: 0 0 1.5rem 0;
  color: var(--text-color);
  line-height: 1.6;
  font-size: clamp(0.9rem, 2.5vw, 1rem);
}

.mode-button {
  background: var(--secondary-color);
  color: white;
  border: none;
  border-radius: 2rem;
  padding: 0.8rem 2rem;
  font-size: clamp(0.9rem, 2.5vw, 1.1rem);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.mode-button:hover {
  background: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.mode-button.single-player:hover {
  background: var(--success-color);
  box-shadow: 0 6px 20px rgba(46, 204, 113, 0.4);
}

.mode-button.two-player:hover {
  background: #8e44ad;
  box-shadow: 0 6px 20px rgba(142, 68, 173, 0.4);
}

.mode-footer {
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid rgba(52, 152, 219, 0.2);
}

.mode-footer p {
  margin: 0;
  color: var(--text-color);
  font-size: clamp(0.8rem, 2vw, 0.9rem);
  font-style: italic;
}

/* Mobile responsiveness for mode selector */
@media (max-width: 768px) {
  .mode-options {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .mode-card {
    padding: 1.5rem;
  }

  .mode-icon {
    font-size: 2.5rem;
  }
}

/* Two-Player Setup Styles */
.two-player-setup {
  width: 100%;
  max-width: 900px;
  padding: var(--base-padding);
  background: #f8f9fa;
  border-radius: 1rem;
  box-shadow:
    0 10px 20px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(0, 0, 0, 0.05);
  transition: transform var(--transition-speed) var(--transition-easing);
  position: relative;
  z-index: 2;
  box-sizing: border-box;
  margin-top: -2rem;
  backdrop-filter: blur(5px);
}

.setup-header {
  text-align: center;
  margin-bottom: 2rem;
}

.setup-header h2 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
  font-size: clamp(1.5rem, 4vw, 2rem);
}

.setup-header p {
  margin: 0;
  color: var(--text-color);
  font-size: clamp(0.9rem, 2.5vw, 1.1rem);
}

.setup-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.setup-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.setup-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--secondary-color);
}

.setup-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.setup-card h3 {
  margin: 0 0 1rem 0;
  color: var(--primary-color);
  font-size: clamp(1.2rem, 3vw, 1.5rem);
}

.setup-card p {
  margin: 0 0 1.5rem 0;
  color: var(--text-color);
  line-height: 1.6;
  font-size: clamp(0.9rem, 2.5vw, 1rem);
}

.form-group {
  margin-bottom: 1rem;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--primary-color);
  font-size: clamp(0.9rem, 2.5vw, 1rem);
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.8rem;
  border: 2px solid #e0e0e0;
  border-radius: 0.5rem;
  font-size: clamp(0.9rem, 2.5vw, 1rem);
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.setup-button {
  background: var(--secondary-color);
  color: white;
  border: none;
  border-radius: 2rem;
  padding: 0.8rem 2rem;
  font-size: clamp(0.9rem, 2.5vw, 1.1rem);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
  width: 100%;
  margin-top: 1rem;
}

.setup-button:hover {
  background: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.create-button:hover {
  background: var(--success-color);
  box-shadow: 0 6px 20px rgba(46, 204, 113, 0.4);
}

.join-button:hover {
  background: #8e44ad;
  box-shadow: 0 6px 20px rgba(142, 68, 173, 0.4);
}

.setup-footer {
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid rgba(52, 152, 219, 0.2);
}

.back-button {
  background: transparent;
  color: var(--text-color);
  border: 2px solid #bdc3c7;
  border-radius: 2rem;
  padding: 0.6rem 1.5rem;
  font-size: clamp(0.8rem, 2vw, 1rem);
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  border-color: var(--secondary-color);
  color: var(--secondary-color);
  transform: translateY(-1px);
}

/* Room Waiting Styles */
.room-waiting {
  width: 100%;
  max-width: 700px;
  padding: var(--base-padding);
  background: #f8f9fa;
  border-radius: 1rem;
  box-shadow:
    0 10px 20px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(0, 0, 0, 0.05);
  transition: transform var(--transition-speed) var(--transition-easing);
  position: relative;
  z-index: 2;
  box-sizing: border-box;
  margin-top: -2rem;
  backdrop-filter: blur(5px);
  text-align: center;
}

.waiting-header {
  margin-bottom: 2rem;
}

.waiting-header h2 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
  font-size: clamp(1.5rem, 4vw, 2rem);
}

.waiting-header p {
  margin: 0;
  color: var(--text-color);
  font-size: clamp(0.9rem, 2.5vw, 1.1rem);
}

.room-info {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.room-code-display,
.invitation-link {
  margin-bottom: 1.5rem;
}

.room-code-display label,
.invitation-link label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--primary-color);
  font-size: clamp(0.9rem, 2.5vw, 1rem);
}

.room-code {
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: center;
}

.code-text {
  font-family: 'Courier New', monospace;
  font-size: clamp(1.2rem, 3vw, 1.5rem);
  font-weight: bold;
  color: var(--primary-color);
  background: rgba(52, 152, 219, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: 2px solid var(--secondary-color);
}

.link-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.invitation-url {
  flex: 1;
  padding: 0.8rem;
  border: 2px solid #e0e0e0;
  border-radius: 0.5rem;
  font-size: clamp(0.8rem, 2vw, 0.9rem);
  background: #f8f9fa;
  color: var(--text-color);
}

.copy-button {
  background: var(--secondary-color);
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.8rem 1rem;
  font-size: clamp(0.8rem, 2vw, 0.9rem);
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.copy-button:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.waiting-status {
  margin: 2rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(52, 152, 219, 0.2);
  border-left: 4px solid var(--secondary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.waiting-status p {
  margin: 0;
  color: var(--text-color);
  font-size: clamp(0.9rem, 2.5vw, 1.1rem);
}

.waiting-actions {
  margin-top: 2rem;
}

.cancel-button {
  background: var(--danger-color);
  color: white;
  border: none;
  border-radius: 2rem;
  padding: 0.8rem 2rem;
  font-size: clamp(0.9rem, 2.5vw, 1.1rem);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.cancel-button:hover {
  background: #c0392b;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

/* Notification Styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--success-color);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
  font-size: clamp(0.9rem, 2.5vw, 1rem);
  max-width: 300px;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}